import { Teacher } from '@/models/Teacher';
import { Classroom } from '@/models/Classroom';
import { Level } from '@/models/Level';

export async function seedInitialData() {
  try {
    // Check if data already exists
    const [teacherCount, classroomCount, levelCount] = await Promise.all([
      Teacher.countDocuments(),
      Classroom.countDocuments(),
      Level.countDocuments(),
    ]);

    if (teacherCount > 0 || classroomCount > 0 || levelCount > 0) {
      console.log('📚 Initial data already exists, skipping seed...');
      return;
    }

    console.log('🌱 Seeding initial data...');

    // Create initial teachers
    const teachers = await Teacher.insertMany([
      { name: 'Mr<PERSON> <PERSON>', color: '#3B82F6' },
      { name: '<PERSON><PERSON>', color: '#EF4444' },
      { name: 'Dr. <PERSON>', color: '#10B981' },
      { name: 'Prof<PERSON>', color: '#F59E0B' },
      { name: 'Mrs. <PERSON>', color: '#8B5CF6' },
    ]);

    // Create initial classrooms
    const classrooms = await Classroom.insertMany([
      { name: 'Room 101', capacity: 30 },
      { name: 'Room 102', capacity: 25 },
      { name: 'Lab 1', capacity: 20 },
      { name: 'Lab 2', capacity: 20 },
      { name: 'Auditorium', capacity: 100 },
      { name: 'Conference Room', capacity: 15 },
    ]);

    // Create initial levels
    const levels = await Level.insertMany([
      { label: 'Beginner', color: '#22C55E' },
      { label: 'Intermediate', color: '#F59E0B' },
      { label: 'Advanced', color: '#EF4444' },
      { label: 'Expert', color: '#8B5CF6' },
    ]);

    console.log(`✅ Seeded ${teachers.length} teachers, ${classrooms.length} classrooms, and ${levels.length} levels`);
  } catch (error) {
    console.error('❌ Error seeding initial data:', error);
    throw error;
  }
}
