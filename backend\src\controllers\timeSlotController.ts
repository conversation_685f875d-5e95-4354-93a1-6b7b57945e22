import { Request, Response, NextFunction } from 'express';
import { TimeSlot } from '@/models/TimeSlot';
import { Teacher } from '@/models/Teacher';
import { Classroom } from '@/models/Classroom';
import { Level } from '@/models/Level';
import { AppError } from '@/utils/AppError';

export class TimeSlotController {
  // Create new time slot
  public async createTimeSlot(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const timeSlotData = req.body;

      // Validate that referenced entities exist
      const [teacher, classroom, level] = await Promise.all([
        Teacher.findById(timeSlotData.teacherId),
        Classroom.findById(timeSlotData.classroomId),
        Level.findById(timeSlotData.levelId),
      ]);

      if (!teacher) {
        throw new AppError('Teacher not found', 404);
      }
      if (!classroom) {
        throw new AppError('Classroom not found', 404);
      }
      if (!level) {
        throw new AppError('Level not found', 404);
      }

      // Check for conflicts
      const conflictingSlots = await TimeSlot.find({
        $or: [
          { teacherId: timeSlotData.teacherId },
          { classroomId: timeSlotData.classroomId }
        ],
        dayOfWeek: timeSlotData.dayOfWeek,
        $or: [
          {
            start: { $lt: timeSlotData.end },
            end: { $gt: timeSlotData.start }
          }
        ]
      });

      if (conflictingSlots.length > 0) {
        throw new AppError('Time slot conflicts with existing schedule', 400);
      }

      const timeSlot = new TimeSlot(timeSlotData);
      await timeSlot.save();

      // Populate the response
      await timeSlot.populate([
        { path: 'teacherId', select: 'name color' },
        { path: 'classroomId', select: 'name capacity' },
        { path: 'levelId', select: 'label color' }
      ]);

      res.status(201).json({
        success: true,
        message: 'Time slot created successfully',
        data: timeSlot.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Get all time slots
  public async getTimeSlots(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const timeSlots = await TimeSlot.find()
        .populate('teacherId', 'name color')
        .populate('classroomId', 'name capacity')
        .populate('levelId', 'label color')
        .sort({ dayOfWeek: 1, start: 1 });

      res.json({
        success: true,
        message: 'Time slots retrieved successfully',
        data: timeSlots,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get time slot by ID
  public async getTimeSlotById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const timeSlot = await TimeSlot.findById(id)
        .populate('teacherId', 'name color')
        .populate('classroomId', 'name capacity')
        .populate('levelId', 'label color');

      if (!timeSlot) {
        throw new AppError('Time slot not found', 404);
      }

      res.json({
        success: true,
        message: 'Time slot retrieved successfully',
        data: timeSlot.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Update time slot
  public async updateTimeSlot(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // If updating references, validate they exist
      if (updateData.teacherId) {
        const teacher = await Teacher.findById(updateData.teacherId);
        if (!teacher) {
          throw new AppError('Teacher not found', 404);
        }
      }
      if (updateData.classroomId) {
        const classroom = await Classroom.findById(updateData.classroomId);
        if (!classroom) {
          throw new AppError('Classroom not found', 404);
        }
      }
      if (updateData.levelId) {
        const level = await Level.findById(updateData.levelId);
        if (!level) {
          throw new AppError('Level not found', 404);
        }
      }

      const timeSlot = await TimeSlot.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      )
        .populate('teacherId', 'name color')
        .populate('classroomId', 'name capacity')
        .populate('levelId', 'label color');

      if (!timeSlot) {
        throw new AppError('Time slot not found', 404);
      }

      res.json({
        success: true,
        message: 'Time slot updated successfully',
        data: timeSlot.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete time slot
  public async deleteTimeSlot(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const timeSlot = await TimeSlot.findByIdAndDelete(id);

      if (!timeSlot) {
        throw new AppError('Time slot not found', 404);
      }

      res.json({
        success: true,
        message: 'Time slot deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  // Get schedule data (bulk endpoint)
  public async getScheduleData(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const [teachers, classrooms, levels, timeSlots] = await Promise.all([
        Teacher.find().sort({ name: 1 }),
        Classroom.find().sort({ name: 1 }),
        Level.find().sort({ label: 1 }),
        TimeSlot.find()
          .populate('teacherId', 'name color')
          .populate('classroomId', 'name capacity')
          .populate('levelId', 'label color')
          .sort({ dayOfWeek: 1, start: 1 })
      ]);

      res.json({
        success: true,
        message: 'Schedule data retrieved successfully',
        data: {
          teachers,
          classrooms,
          levels,
          timeSlots,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
